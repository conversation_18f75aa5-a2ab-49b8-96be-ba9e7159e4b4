import { PlaceholderPattern } from '@/components/ui/placeholder-pattern';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { Plus, Pencil, Trash2 } from 'lucide-react';
import DataTable, { TableColumn } from 'react-data-table-component';
import { PageProps } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Berita',
        href: '/berita',
    },
];

export default function Dashboard() {
    const props = usePage().props as unknown as { berita?: any[] };
    const berita = props.berita ?? [];

    const [formData, setFormData] = useState({
        judul: '',
        deskripsi: '',
        tanggal: '',
        gambar: null as File | null,
    });
    const [isOpen, setIsOpen] = useState(false);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const target = e.target as HTMLInputElement;

        if (target.name === 'gambar' && target.files && target.files.length > 0) {
            const file = target.files[0];
            setFormData((prev) => ({ ...prev, gambar: file }));
        } else {
            setFormData((prev) => ({ ...prev, [target.name]: target.value }));
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const data = new FormData();
        data.append('judul', formData.judul);
        data.append('deskripsi', formData.deskripsi);
        data.append('tanggal', formData.tanggal);
        if (formData.gambar) {
            data.append('gambar', formData.gambar);
        }

        router.post('/add/berita', data, {
            forceFormData: true,
            onSuccess: () => {
                setFormData({ judul: '', deskripsi: '', tanggal: '', gambar: null });
                setIsOpen(false);
            },
        });
    };

    const handleEdit = (item: any) => {
        // Implementasi fitur edit nanti
    };

    const handleDelete = (id: number) => {
        if (confirm('Yakin ingin menghapus berita ini?')) {
            router.delete(`/delete/berita/${id}`);
        }
    };

    const columns: TableColumn<any>[] = [
        {
            name: 'Judul Berita',
            center: true,
            sortable: true,
            cell: row => (
                <span className="inline-block px-3 py-1 text-xs font-semibold text-blue-700 bg-blue-100 rounded dark:bg-blue-900 dark:text-blue-100">
                    {row.judul_berita}
                </span>
            ),
        },
        {
            name: 'Deskripsi Berita',
            selector: row => row.deskripsi_berita,
            sortable: true,
            center: true,
            wrap: true, // Membungkus teks panjang
            cell: row => (
                <div
                    title={row.deskripsi_berita}
                    className="max-w-xs truncate text-sm"
                >
                    {row.deskripsi_berita}
                </div>
            ),
            width: '200px',
        },
        {
            name: 'Tanggal Terbit',
            center: true,
            sortable: true,
            cell: row => (
                <span className="inline-block px-3 py-1 text-xs font-semibold text-red-700 bg-red-100 rounded dark:bg-blue-900 dark:text-blue-100">
                    {row.tanggal_berita}
                </span>
            ),
        },
        {
            name: 'Gambar',
            cell: row => row.gambar ? (
                <img
                    src={row.gambar}
                    alt={row.judul_berita}
                    className="h-16 w-24 object-cover rounded shadow mx-auto m-3"
                />
            ) : (
                <span className="text-sm text-gray-400 italic">Tidak ada gambar</span>
            ),
            center: true,
        },
        {
            name: 'By Admin',
            selector: row => row.user?.name ?? '-',
            sortable: true,
            center: true,
        },
        {
            name: 'Aksi',
            cell: row => (
                <div className="space-x-2">
                    <button
                        onClick={() => handleEdit(row)}
                        className="p-2 text-white bg-yellow-500 rounded hover:bg-yellow-600"
                        title="Edit"
                    >
                        <Pencil className="h-4 w-4" />
                    </button>
                    <button
                        onClick={() => handleDelete(row.id)}
                        className="p-2 text-white bg-red-600 rounded hover:bg-red-700"
                        title="Hapus"
                    >
                        <Trash2 className="h-4 w-4" />
                    </button>
                </div>
            ),
            center: true,
        }
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Berita" />
            <div className="p-4 space-y-6">
                <div className="flex justify-between items-center">
                    <h2 className="text-xl font-semibold">Data Berita</h2>
                    <button
                        onClick={() => setIsOpen(true)}
                        className="flex items-center gap-2 rounded bg-blue-600 px-3 py-2 text-white hover:bg-blue-700"
                    >
                        <Plus className="h-4 w-4" /> Tambah Berita
                    </button>
                </div>

                {/* Tabel Data Berita */}
                <DataTable
                    columns={columns}
                    data={berita}
                    pagination
                    highlightOnHover
                    striped
                    dense
                    responsive
                    customStyles={{
                        headCells: {
                            style: {
                                fontWeight: 'bold',
                                textTransform: 'uppercase',
                                justifyContent: 'center',
                                background: 'black',
                                color: 'white',
                                height: '60px'
                            },
                        },
                        cells: {
                            style: {
                                justifyContent: 'center'
                            },
                        },
                    }}
                />

                {/* Modal Form Tambah Berita */}
                <Dialog open={isOpen} onClose={() => setIsOpen(false)} className="relative z-50">
                    <div className="fixed inset-0 bg-black/50" aria-hidden="true" />
                    <div className="fixed inset-0 flex items-center justify-center p-4">
                        <Dialog.Panel className="w-full max-w-xl rounded-lg bg-white p-6 shadow-xl dark:bg-gray-900">
                            <Dialog.Title className="text-lg font-semibold mb-4">Tambah Berita</Dialog.Title>
                            <form onSubmit={handleSubmit} className="space-y-4" encType="multipart/form-data">
                                <div>
                                    <label className="block text-sm font-medium mb-1">Judul Berita</label>
                                    <input
                                        type="text"
                                        name="judul"
                                        value={formData.judul}
                                        onChange={handleChange}
                                        className="w-full rounded border border-gray-300 px-3 py-2 dark:bg-gray-800 dark:border-gray-600"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Deskripsi</label>
                                    <textarea
                                        name="deskripsi"
                                        value={formData.deskripsi}
                                        onChange={handleChange}
                                        className="w-full rounded border border-gray-300 px-3 py-2 dark:bg-gray-800 dark:border-gray-600"
                                        rows={4}
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Tanggal</label>
                                    <input
                                        type="date"
                                        name="tanggal"
                                        value={formData.tanggal}
                                        onChange={handleChange}
                                        className="w-full rounded border border-gray-300 px-3 py-2 dark:bg-gray-800 dark:border-gray-600"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Gambar</label>
                                    <input
                                        type="file"
                                        name="gambar"
                                        onChange={handleChange}
                                        className="w-full rounded border border-gray-300 px-3 py-2 dark:bg-gray-800 dark:border-gray-600"
                                        accept="image/*"
                                        required
                                    />
                                </div>

                                <div className="flex justify-end gap-2">
                                    <button
                                        type="button"
                                        onClick={() => setIsOpen(false)}
                                        className="px-4 py-2 rounded border text-sm dark:border-gray-600"
                                    >
                                        Batal
                                    </button>
                                    <button
                                        type="submit"
                                        className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 text-sm"
                                    >
                                        Simpan
                                    </button>
                                </div>
                            </form>
                        </Dialog.Panel>
                    </div>
                </Dialog>
            </div>
        </AppLayout>
    );
}
