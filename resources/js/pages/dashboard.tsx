import { Head, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faNewspaper, faTrophy, faUsers } from '@fortawesome/free-solid-svg-icons';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard() {
    const page = usePage<{
        jumlah_berita: number;
        jumlah_prestasi: number;
        jumlah_user: number;
    }>();
    const { jumlah_berita, jumlah_prestasi, jumlah_user } = page.props;

    const cards = [
        {
            title: 'Jumlah Berita',
            count: jumlah_berita,
            icon: faNewspaper,
            color: 'bg-blue-500',
        },
        {
            title: 'Jumlah Prestasi',
            count: jumlah_prestasi,
            icon: faTrophy,
            color: 'bg-green-500',
        },
        {
            title: 'Jumlah User',
            count: jumlah_user,
            icon: faUsers,
            color: 'bg-purple-500',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="p-4 space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                    {cards.map((card, i) => (
                        <div
                            key={i}
                            className="rounded-xl p-6 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow hover:shadow-lg transition-all"
                        >
                            <div className="flex items-center gap-4">
                                <div className={`p-3 rounded-full text-white ${card.color}`}>
                                    <FontAwesomeIcon icon={card.icon} className="w-6 h-6" />
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">{card.title}</p>
                                    <h2 className="text-2xl font-bold text-gray-800 dark:text-white">{card.count}</h2>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Tempat konten tambahan */}
                <div className="min-h-[300px] rounded-xl border border-dashed border-gray-300 dark:border-gray-600 p-6">
                    <p className="text-gray-500 dark:text-gray-400">Konten tambahan dashboard di sini...</p>
                </div>
            </div>
        </AppLayout>
    );
}
