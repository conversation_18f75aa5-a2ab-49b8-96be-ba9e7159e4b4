import { Head, router, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import DataTable, { TableColumn } from 'react-data-table-component';
import { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { Trash2 } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Manajemen User',
        href: '/manage-user',
    },
];

interface User {
    id: number;
    name: string;
    email: string;
}

export default function ManageUser() {
    const { users } = usePage().props as { users?: User[] };
    const [isOpen, setIsOpen] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        password: ''
    });

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        router.post('/users', formData, { forceFormData: true,
            onSuccess: () => {
                setIsOpen(false);
                setFormData({ name: '', email: '', password: '' });
            },
        });
    };

    const columns: TableColumn<User>[] = [
        {
            name: 'No',
            cell: (_, index) => index + 1,
            width: '70px',
            center: true,
        },
        {
            name: 'Nama',
            selector: row => row.name,
            sortable: true,
        },
        {
            name: 'Email',
            selector: row => row.email,
        },
        {
            name: 'Aksi',
            cell: row => (
                <div className="flex gap-2">
                    <a href={`/users/${row.id}/delete`} className="px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700">
                        <Trash2 className="h-4 w-4" />
                    </a>
                </div>
            ),
            center: true,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Manajemen User" />
            <div className="p-4 space-y-4">
                <div className="flex justify-between items-center mb-2">
                    <h2 className="text-lg font-semibold">Daftar User</h2>
                    <button
                        onClick={() => setIsOpen(true)}
                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
                    >
                        + Tambah User
                    </button>
                </div>

                <DataTable
                    columns={columns}
                    data={users}
                    pagination
                    highlightOnHover
                    striped
                    responsive
                />

                {/* Modal Tambah User */}
                <Dialog open={isOpen} onClose={() => setIsOpen(false)} className="relative z-50">
                    <div className="fixed inset-0 bg-black/40" aria-hidden="true" />
                    <div className="fixed inset-0 flex items-center justify-center p-4">
                        <Dialog.Panel className="w-full max-w-md rounded bg-white p-6 shadow-lg dark:bg-gray-900">
                            <Dialog.Title className="text-lg font-semibold mb-4">Tambah User Baru</Dialog.Title>
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium">Nama</label>
                                    <input
                                        type="text"
                                        name="name"
                                        value={formData.name}
                                        onChange={handleChange}
                                        className="w-full border px-3 py-2 rounded dark:bg-gray-800"
                                        required
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium">Email</label>
                                    <input
                                        type="email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleChange}
                                        className="w-full border px-3 py-2 rounded dark:bg-gray-800"
                                        required
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium">Password</label>
                                    <input
                                        type="password"
                                        name="password"
                                        value={formData.password}
                                        onChange={handleChange}
                                        className="w-full border px-3 py-2 rounded dark:bg-gray-800"
                                        required
                                    />
                                </div>

                                <div className="flex justify-end gap-2">
                                    <button
                                        type="button"
                                        onClick={() => setIsOpen(false)}
                                        className="px-4 py-2 border rounded text-sm"
                                    >
                                        Batal
                                    </button>
                                    <button
                                        type="submit"
                                        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm"
                                    >
                                        Simpan
                                    </button>
                                </div>
                            </form>
                        </Dialog.Panel>
                    </div>
                </Dialog>
            </div>
        </AppLayout>
    );
}
