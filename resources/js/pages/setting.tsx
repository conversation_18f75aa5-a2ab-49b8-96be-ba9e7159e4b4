import { PlaceholderPattern } from '@/components/ui/placeholder-pattern';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, Settings, Upload, FileText, Type } from 'lucide-react';
import { useState, useEffect } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Setting',
        href: '/setting',
    },
];

export default function Setting() {
    const [isOpen, setIsOpen] = useState(true);
    const [csrfToken, setCsrfToken] = useState('');

    useEffect(() => {
        // Get CSRF token from meta tag
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (token) {
            setCsrfToken(token);
        }
    }, []);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Setting" />
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6 overflow-x-auto">
                <Collapsible open={isOpen} onOpenChange={setIsOpen}>
                    <Card className="w-full shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
                        <CollapsibleTrigger asChild>
                            <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200 rounded-t-xl">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-md">
                                            <Settings className="h-5 w-5 text-white" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                                Pengaturan Sistem
                                            </CardTitle>
                                            <CardDescription className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                Kelola pengaturan dan konfigurasi aplikasi
                                            </CardDescription>
                                        </div>
                                    </div>
                                    <ChevronDown
                                        className={`h-5 w-5 text-gray-500 transition-transform duration-200 ${
                                            isOpen ? 'rotate-180' : ''
                                        }`}
                                    />
                                </div>
                            </CardHeader>
                        </CollapsibleTrigger>

                        <CollapsibleContent>
                            <CardContent className="p-6 pt-0">
                                <form action="/add/header" method="POST" encType="multipart/form-data" className="space-y-8">
                                    {/* CSRF Token */}
                                    <input type="hidden" name="_token" value={csrfToken} />
                                    <div className="grid gap-8 lg:grid-cols-3">
                                        {/* Title Header Section */}
                                        <div className="lg:col-span-3">
                                            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
                                                <div className="flex items-center gap-3 mb-4">
                                                    <div className="p-2 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg">
                                                        <Type className="h-4 w-4 text-white" />
                                                    </div>
                                                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                                                        Title Header
                                                    </h3>
                                                </div>
                                                <div className="space-y-3">
                                                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                        Masukkan judul header
                                                    </label>
                                                    <input
                                                        type="text"
                                                        name="title"
                                                        id="title"
                                                        className="block w-full rounded-lg border border-gray-300 dark:border-gray-600
                                                            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                                                            shadow-sm placeholder-gray-400 dark:placeholder-gray-500
                                                            focus:ring-2 focus:ring-blue-500 focus:border-transparent
                                                            transition-all duration-200
                                                            text-sm leading-relaxed p-4"
                                                        placeholder="Masukkan judul header untuk aplikasi..."
                                                    />
                                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                                        Judul ini akan ditampilkan sebagai header utama aplikasi.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Upload Gambar Section */}
                                        <div className="lg:col-span-1">
                                            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
                                                <div className="flex items-center gap-3 mb-4">
                                                    <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                                                        <Upload className="h-4 w-4 text-white" />
                                                    </div>
                                                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                                                        Upload Gambar
                                                    </h3>
                                                </div>
                                                <div className="space-y-3">
                                                    <label htmlFor="image" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                        Pilih file gambar
                                                    </label>
                                                    <div className="relative">
                                                        <input
                                                            type="file"
                                                            name="image"
                                                            id="image"
                                                            accept="image/*"
                                                            multiple
                                                            className="block w-full text-sm text-gray-500 dark:text-gray-400
                                                                file:mr-4 file:py-3 file:px-4
                                                                file:rounded-lg file:border-0
                                                                file:text-sm file:font-medium
                                                                file:bg-gradient-to-r file:from-blue-50 file:to-indigo-50
                                                                file:text-blue-700 dark:file:from-blue-900 dark:file:to-indigo-900
                                                                dark:file:text-blue-300
                                                                hover:file:from-blue-100 hover:file:to-indigo-100
                                                                dark:hover:file:from-blue-800 dark:hover:file:to-indigo-800
                                                                file:cursor-pointer file:transition-all file:duration-200
                                                                border border-gray-300 dark:border-gray-600 rounded-lg
                                                                focus:ring-2 focus:ring-blue-500 focus:border-transparent
                                                                bg-white dark:bg-gray-700"
                                                        />
                                                    </div>
                                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                                        Mendukung format: JPG, PNG, GIF. Maksimal 5MB per file.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Deskripsi Section */}
                                        <div className="lg:col-span-2">
                                            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
                                                <div className="flex items-center gap-3 mb-4">
                                                    <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg">
                                                        <FileText className="h-4 w-4 text-white" />
                                                    </div>
                                                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                                                        Deskripsi
                                                    </h3>
                                                </div>
                                                <div className="space-y-3">
                                                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                        Masukkan deskripsi detail
                                                    </label>
                                                    <textarea
                                                        name="deskripsi"
                                                        id="description"
                                                        rows={6}
                                                        className="block w-full rounded-lg border border-gray-300 dark:border-gray-600
                                                            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                                                            shadow-sm placeholder-gray-400 dark:placeholder-gray-500
                                                            focus:ring-2 focus:ring-blue-500 focus:border-transparent
                                                            transition-all duration-200 resize-none
                                                            text-sm leading-relaxed p-4"
                                                        placeholder="Masukkan deskripsi lengkap tentang pengaturan atau konfigurasi yang ingin Anda lakukan..."
                                                    ></textarea>
                                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                                        Berikan deskripsi yang jelas dan detail untuk memudahkan pengelolaan.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Submit Button */}
                                    <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                                        <button
                                            type="submit"
                                            className="inline-flex items-center gap-2 px-6 py-3
                                                bg-gradient-to-r from-blue-600 to-indigo-600
                                                hover:from-blue-700 hover:to-indigo-700
                                                text-white text-sm font-medium rounded-lg
                                                shadow-lg hover:shadow-xl
                                                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                                transition-all duration-200 transform hover:scale-105"
                                        >
                                            <Settings className="h-4 w-4" />
                                            Simpan Pengaturan
                                        </button>
                                    </div>
                                </form>
                            </CardContent>
                        </CollapsibleContent>
                    </Card>
                </Collapsible>
            </div>
        </AppLayout>
    );
}
