import { Head, usePage } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import DataTable, { TableColumn } from 'react-data-table-component';
import { Trash2 } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Data Header',
        href: '/headerData',
    },
];

interface HeaderItem {
    id: number;
    title_header: string;
    header_deskripsi: string;
    header_images: string;
}

export default function Header() {
    const page = usePage().props as { data?: HeaderItem[] };
    const data = page.data ?? [];

    const columns: TableColumn<HeaderItem>[] = [
        {
            name: 'No',
            cell: (_, index) => index + 1,
            width: '60px',
            center: true,
        },
        {
            name: 'Title',
            selector: row => row.title_header,
            sortable: true,
            wrap: true,
            grow: 2,
        },
        {
            name: '<PERSON><PERSON><PERSON><PERSON>',
            selector: row => row.header_deskripsi,
            sortable: false,
            wrap: true,
            grow: 3,
        },
        {
            name: 'Image',
            cell: row => (
                <div className="flex justify-center items-center">
                    <img
                        src={`/${row.header_images}`}
                        alt={row.title_header}
                        className="w-16 h-16 object-cover rounded border border-gray-300"
                        onError={(e) => {
                            e.currentTarget.src = 'https://via.placeholder.com/80';
                        }}
                    />
                </div>
            ),
            center: true,
            width: '120px',
        },
        {
            name: 'Aksi',
            cell: row => (
                <div className="flex gap-2 justify-center w-full items-center">
                    <a 
                        href={`/header/${row.id}/delete`} 
                        className="px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-xs rounded-md shadow"
                    >
                        <Trash2 className="h-4 w-4" />
                    </a>
                </div>
            ),
            center: true,
            width: '150px',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Data Header" />
            <div className="p-4 overflow-x-auto">
                <DataTable
                    title="Daftar Header"
                    columns={columns}
                    data={data}
                    pagination
                    highlightOnHover
                    striped
                    responsive
                    persistTableHead
                    customStyles={{
                        headCells: {
                            style: {
                                fontWeight: 'bold',
                                textTransform: 'uppercase',
                                backgroundColor: 'black',
                                color: 'white',
                                fontSize: '13px',
                                justifyContent: 'center',
                            },
                        },
                        cells: {
                            style: {
                                fontSize: '14px',
                                justifyContent: 'center',
                            },
                        },
                        rows: {
                            style: {
                                minHeight: '72px',
                            },
                        },
                    }}
                />
            </div>
        </AppLayout>
    );
}
