import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Calendar, ChevronRight } from 'lucide-react';
import { router } from '@inertiajs/react';

interface BeritaItem {
    id: number;
    judul: string;
    tanggal: string;
    gambar: string;
    excerpt: string;
    kategori: string;
    author?: string;
    isNew?: boolean;
}

interface NewsProps {
    initialBerita?: BeritaItem[];
}

export default function NewsSection({ initialBerita = [] }: NewsProps) {
    const [beritaData, setBeritaData] = useState<BeritaItem[]>(initialBerita);

    // Function to handle "Baca Selengkapnya" click
    const handleReadMore = (beritaId: number) => {
        router.visit(`/berita/${beritaId}`);
    };

    // Fetch berita data once when component mounts
    useEffect(() => {
        const fetchBeritaData = async () => {
            try {
                const response = await fetch('/api/berita');
                const result = await response.json();

                if (result.success && result.data) {
                    setBeritaData(result.data);
                }
            } catch (error) {
                console.error('Error fetching berita data:', error);
                // Keep using fallback data if fetch fails
            }
        };

        fetchBeritaData();
    }, []); // Empty dependency array means this runs only once on mount

    return (
        <section id="berita" className="py-20 bg-gray-50 dark:bg-gray-800">
            <div className="mx-auto max-w-7xl px-4">
                <motion.div
                    className="text-center mb-16"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <div className="flex items-center justify-center gap-3 mb-4">
                        <h3 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">
                            Berita & Kegiatan Terkini
                        </h3>
                        <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                                Live
                            </span>
                        </div>
                    </div>
                    <p className="text-lg text-gray-700 dark:text-gray-200 max-w-2xl mx-auto">
                        Ikuti perkembangan terbaru dan kegiatan menarik di sekolah kami
                    </p>
                </motion.div>

                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {beritaData.map((item, index) => (
                        <motion.article
                            key={item.id}
                            className="bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group border border-gray-100 dark:border-gray-800 hover:border-green-200 dark:hover:border-green-700"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                            viewport={{ once: true }}
                            whileHover={{ y: -5 }}
                        >
                            <div className="relative overflow-hidden">
                                <img
                                    src={item.gambar}
                                    alt={item.judul}
                                    className="h-48 w-full object-cover group-hover:scale-110 transition-transform duration-500"
                                />
                                <div className="absolute top-4 left-4 flex gap-2">
                                    <span className="bg-green-600 text-white px-3 py-1 rounded-full text-xs font-semibold transition-colors duration-200">
                                        {item.kategori}
                                    </span>
                                    {item.isNew && (
                                        <span className="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-semibold animate-pulse">
                                            NEW
                                        </span>
                                    )}
                                </div>
                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </div>

                            <div className="p-6">
                                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300 mb-3">
                                    <Calendar className="w-4 h-4" />
                                    <span>{item.tanggal}</span><span className='font-bold badge bg-blue-500 text-white rounded px-1'>By {item.author}</span>
                                </div>

                                <h4 className="text-xl font-bold mb-3 text-gray-800 dark:text-white group-hover:text-green-600 transition-colors duration-300">
                                    {item.judul}
                                </h4>

                                <p className="text-gray-700 dark:text-gray-200 text-sm mb-4 line-clamp-3 leading-relaxed">
                                    {item.excerpt}
                                </p>

                                <button
                                    onClick={() => handleReadMore(item.id)}
                                    className="flex items-center gap-2 text-green-600 hover:text-green-700 font-semibold text-sm group-hover:gap-3 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 rounded-md px-1 py-1"
                                >
                                    Baca Selengkapnya
                                    <ChevronRight className="w-4 h-4" />
                                </button>
                            </div>
                        </motion.article>
                        ))}
                </div>

                <motion.div
                    className="text-center mt-12"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    viewport={{ once: true }}
                >
                    <button className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                        Lihat Semua Berita
                    </button>
                </motion.div>
            </div>
        </section>
    );
}