import { motion } from 'framer-motion';
import { Users, BookOpen, Award, Globe } from 'lucide-react';

export default function AboutSection() {
    const features = [
        {
            icon: Users,
            title: "Tenaga Pengajar Berkualitas",
            description: "Guru-guru berpeng<PERSON>man dan bersertifikat"
        },
        {
            icon: BookOpen,
            title: "Kurikulum Modern",
            description: "Pembelajaran yang mengikuti perkembangan zaman"
        },
        {
            icon: Award,
            title: "Prestasi Gemilang",
            description: "Berbagai pencapaian di tingkat nasional"
        },
        {
            icon: Globe,
            title: "Wawasan Global",
            description: "Mempersiapkan siswa untuk era digital"
        }
    ];

    return (
        <section id="tentang" className="py-20 bg-gradient-to-br from-gray-50 via-white to-green-50/30 dark:from-gray-800 dark:via-gray-900 dark:to-green-900/10 relative overflow-hidden">
            {/* Background Decorations */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-green-200/20 to-blue-200/20 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-yellow-200/20 to-green-200/20 rounded-full blur-3xl"></div>
            </div>

            <div className="mx-auto max-w-7xl px-4 relative z-10">
                {/* Section Header */}
                <motion.div
                    className="text-center mb-16"
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                >
                    <motion.h3
                        className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-green-900 to-green-700 bg-clip-text text-transparent"
                        whileHover={{ scale: 1.05 }}
                        transition={{ duration: 0.3 }}
                    >
                        Tentang MTs Negeri 4 Gunungkidul
                    </motion.h3>
                    <motion.div
                        className="w-24 h-1 bg-gradient-to-r from-green-900 to-green-500 mx-auto rounded-full mb-6"
                        initial={{ width: 0 }}
                        whileInView={{ width: 96 }}
                        transition={{ duration: 0.8, delay: 0.3 }}
                        viewport={{ once: true }}
                    />
                    <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
                        Institusi pendidikan yang berkomitmen menghasilkan generasi cerdas, berkarakter, dan siap menghadapi masa depan
                    </p>
                </motion.div>

                <div className="grid lg:grid-cols-2 gap-16 items-start">
                    {/* Left Content */}
                    <motion.div
                        initial={{ opacity: 0, x: -30 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.8, staggerChildren: 0.2 }}
                        viewport={{ once: true }}
                        className="space-y-8"
                    >
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                            viewport={{ once: true }}
                            className="space-y-6"
                        >
                            <p className="text-lg text-gray-700 dark:text-gray-200 leading-relaxed">
                                MTs Negeri 4 Gunungkidul adalah institusi pendidikan yang berkomitmen untuk menghasilkan
                                generasi muda yang cerdas, berkarakter, dan siap menghadapi tantangan masa depan.
                                Dengan fasilitas modern dan tenaga pengajar berpengalaman, kami memberikan pendidikan
                                berkualitas tinggi yang mengintegrasikan akademik, karakter, dan teknologi.
                            </p>
                            <p className="text-lg text-gray-700 dark:text-gray-200 leading-relaxed">
                                Kami percaya bahwa setiap siswa memiliki potensi unik yang perlu dikembangkan melalui
                                pendekatan pembelajaran yang holistik dan inovatif.
                            </p>
                        </motion.div>

                        {/* Features Grid */}
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.4 }}
                            viewport={{ once: true }}
                            className="grid sm:grid-cols-2 gap-4"
                        >
                            {features.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    whileInView={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                                    viewport={{ once: true }}
                                    whileHover={{ scale: 1.02, y: -5 }}
                                    className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 group cursor-pointer"
                                >
                                    <div className="flex items-start gap-4">
                                        <div className="bg-gradient-to-r from-green-500 to-blue-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300">
                                            <feature.icon className="w-6 h-6 text-white" />
                                        </div>
                                        <div className="flex-1">
                                            <h4 className="font-bold text-gray-800 dark:text-white mb-2 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">
                                                {feature.title}
                                            </h4>
                                            <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                                                {feature.description}
                                            </p>
                                        </div>
                                    </div>
                                </motion.div>
                            ))}
                        </motion.div>
                    </motion.div>
                    {/* Right Content - Vision & Mission */}
                    <motion.div
                        initial={{ opacity: 0, x: 30 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.8, delay: 0.2 }}
                        viewport={{ once: true }}
                        className="relative"
                    >
                        <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm p-8 rounded-3xl shadow-2xl border border-gray-100 dark:border-gray-700 relative overflow-hidden">
                            {/* Card Background Decoration */}
                            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-green-200/30 to-blue-200/30 rounded-full -translate-y-16 translate-x-16"></div>
                            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-yellow-200/30 to-green-200/30 rounded-full translate-y-12 -translate-x-12"></div>

                            <div className="relative z-10">
                                {/* Vision Section */}
                                <motion.div
                                    className="mb-10"
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: 0.3 }}
                                    viewport={{ once: true }}
                                >
                                    <div className="flex items-center gap-4 mb-6">
                                        <motion.div
                                            className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg"
                                            whileHover={{ scale: 1.1, rotate: 5 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                        </motion.div>
                                        <h4 className="text-2xl font-bold text-gray-800 dark:text-white">Visi</h4>
                                    </div>
                                    <motion.div
                                        className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-6 rounded-2xl border-l-4 border-green-500 shadow-sm"
                                        whileHover={{ scale: 1.02, y: -2 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <p className="text-gray-700 dark:text-gray-200 leading-relaxed font-medium text-lg">
                                            Terwujudnya peserta didik yang bertakwa, cerdas, kreatif, inovatif dan berbudaya
                                        </p>
                                    </motion.div>
                                </motion.div>

                                {/* Mission Section */}
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: 0.4 }}
                                    viewport={{ once: true }}
                                >
                                    <div className="flex items-center gap-4 mb-6">
                                        <motion.div
                                            className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl shadow-lg"
                                            whileHover={{ scale: 1.1, rotate: -5 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                            </svg>
                                        </motion.div>
                                        <h4 className="text-2xl font-bold text-gray-800 dark:text-white">Misi</h4>
                                    </div>

                                    <div className="space-y-6">
                                        {[
                                            {
                                                title: "Bertakwa",
                                                description: "Terwujudnya kesadaran siswa dalam melaksanakan ibadah (sholat wajib, sholat dhuha, puasa, tadarus al-Qur'an) dan penerapan nilai-nilai ajaran agama dengan penuh rasa tanggung jawab.",
                                                color: "from-blue-500 to-blue-600"
                                            },
                                            {
                                                title: "Cerdas",
                                                description: "Terwujudnya generasi yang memiliki perkembangan akal budi, dan pola pikir yang lebih baik.",
                                                color: "from-purple-500 to-purple-600"
                                            },
                                            {
                                                title: "Kreatif",
                                                description: "Terwujudnya generasi yang memiliki pengetahuan dan keterampilan yang mampu menciptakan hasil karya di bidang teknologi informasi, seni dan budaya.",
                                                color: "from-pink-500 to-pink-600"
                                            },
                                            {
                                                title: "Inovatif",
                                                description: "Terwujudnya generasi yang memiliki pengetahuan dan keterampilan serta mampu menemukan karya-karya baru di bidang teknologi informasi, seni dan budaya.",
                                                color: "from-indigo-500 to-indigo-600"
                                            },
                                            {
                                                title: "Berbudaya",
                                                description: "Terwujudnya karakter siswa yang taat tata tertib, peduli lingkungan, cinta budaya bangsa.",
                                                color: "from-teal-500 to-teal-600"
                                            }
                                        ].map((item, index) => (
                                            <motion.div
                                                key={index}
                                                className="group"
                                                initial={{ opacity: 0, x: -20 }}
                                                whileInView={{ opacity: 1, x: 0 }}
                                                transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                                                viewport={{ once: true }}
                                                whileHover={{ x: 5 }}
                                            >
                                                <div className="bg-gray-50 dark:bg-gray-700/50 p-5 rounded-xl border-l-4 border-transparent group-hover:border-green-500 transition-all duration-300 group-hover:shadow-md">
                                                    <div className="flex items-start gap-3">
                                                        <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${item.color} mt-2 flex-shrink-0 group-hover:scale-125 transition-transform duration-300`}></div>
                                                        <div className="flex-1">
                                                            <h5 className="font-bold text-gray-800 dark:text-white mb-2 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">
                                                                {item.title}
                                                            </h5>
                                                            <p className="text-gray-600 dark:text-gray-300 leading-relaxed text-sm">
                                                                {item.description}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </motion.div>
                                        ))}
                                    </div>
                                </motion.div>
                            </div>
                        </div>
                    </motion.div>
                </div>
            </div>
        </section>
    );
}