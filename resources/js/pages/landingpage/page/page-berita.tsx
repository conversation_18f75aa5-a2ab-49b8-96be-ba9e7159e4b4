import { useState } from 'react';
import { usePage, router } from '@inertiajs/react';
import { motion } from 'framer-motion';
import {
    ArrowLeft,
    Calendar,
    User,
    Clock,
    Share2,
    Share,
    MessageCircle,
    Globe,
    Copy,
    Check
} from 'lucide-react';

interface BeritaData {
    id: number;
    judul: string;
    deskripsi: string;
    tanggal: string;
    gambar: string;
    author: string;
    created_at: string;
    updated_at: string;
}

interface PageProps {
    berita: BeritaData;
    [key: string]: any;
}

export default function PageBerita() {
    const { props } = usePage<PageProps>();
    const { berita } = props;
    const [copied, setCopied] = useState(false);

    // Function to go back to landing page
    const handleGoBack = () => {
        router.visit('/');
    };

    // Function to share article
    const handleShare = async (platform?: string) => {
        const url = window.location.href;
        const title = berita.judul;
        const text = `Baca berita: ${title}`;

        if (platform === 'facebook') {
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        } else if (platform === 'twitter') {
            window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank');
        } else if (platform === 'linkedin') {
            window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank');
        } else if (platform === 'copy') {
            try {
                await navigator.clipboard.writeText(url);
                setCopied(true);
                setTimeout(() => setCopied(false), 2000);
            } catch (err) {
                console.error('Failed to copy: ', err);
            }
        }
    };

    // Format content with line breaks
    const formatContent = (content: string) => {
        return content.split('\n').map((paragraph, index) => (
            <p key={index} className="mb-4 text-gray-700 dark:text-gray-300 leading-relaxed">
                {paragraph}
            </p>
        ));
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
            {/* Header Navigation - Sticky with backdrop blur */}
            <header className="sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50 shadow-sm">
                <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center justify-between">
                        <motion.button
                            onClick={handleGoBack}
                            className="group flex items-center gap-3 px-4 py-2 text-gray-700 dark:text-gray-200 hover:text-green-600 dark:hover:text-green-400 bg-gray-100/50 dark:bg-gray-800/50 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-xl transition-all duration-300 border border-gray-200/50 dark:border-gray-700/50 hover:border-green-200 dark:hover:border-green-700/50"
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                        >
                            <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-300" />
                            <span className="font-medium hidden sm:block">Kembali ke Beranda</span>
                            <span className="font-medium sm:hidden">Kembali</span>
                        </motion.button>

                        {/* Share buttons - responsive */}
                        <div className="flex items-center gap-2 sm:gap-3">
                            <span className="text-sm text-gray-500 dark:text-gray-400 hidden sm:block">Bagikan:</span>
                            <div className="flex items-center gap-1 sm:gap-2">
                                <motion.button
                                    onClick={() => handleShare('facebook')}
                                    className="p-2 sm:p-2.5 text-blue-600 hover:text-white hover:bg-blue-600 dark:hover:bg-blue-500 rounded-xl transition-all duration-300 border border-blue-200 dark:border-blue-700/50 hover:border-blue-600 hover:shadow-lg hover:shadow-blue-500/25"
                                    title="Share to Facebook"
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                >
                                    <Share className="w-4 h-4" />
                                </motion.button>
                                <motion.button
                                    onClick={() => handleShare('twitter')}
                                    className="p-2 sm:p-2.5 text-sky-500 hover:text-white hover:bg-sky-500 dark:hover:bg-sky-400 rounded-xl transition-all duration-300 border border-sky-200 dark:border-sky-700/50 hover:border-sky-500 hover:shadow-lg hover:shadow-sky-500/25"
                                    title="Share to Twitter"
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                >
                                    <MessageCircle className="w-4 h-4" />
                                </motion.button>
                                <motion.button
                                    onClick={() => handleShare('linkedin')}
                                    className="p-2 sm:p-2.5 text-blue-700 hover:text-white hover:bg-blue-700 dark:hover:bg-blue-600 rounded-xl transition-all duration-300 border border-blue-200 dark:border-blue-700/50 hover:border-blue-700 hover:shadow-lg hover:shadow-blue-700/25"
                                    title="Share to LinkedIn"
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                >
                                    <Globe className="w-4 h-4" />
                                </motion.button>
                                <motion.button
                                    onClick={() => handleShare('copy')}
                                    className={`p-2 sm:p-2.5 rounded-xl transition-all duration-300 border hover:shadow-lg ${
                                        copied
                                            ? 'text-green-600 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700/50'
                                            : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 border-gray-200 dark:border-gray-700/50 hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-gray-500/25'
                                    }`}
                                    title="Copy Link"
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                >
                                    {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                                </motion.button>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
                <motion.article
                    className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-3xl shadow-2xl shadow-gray-900/10 dark:shadow-black/20 overflow-hidden border border-white/20 dark:border-gray-700/30"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                >
                    {/* Article Header with improved responsive image */}
                    <div className="relative overflow-hidden">
                        <div className="aspect-w-16 aspect-h-9 sm:aspect-h-10 lg:aspect-h-8">
                            <img
                                src={berita.gambar}
                                alt={berita.judul}
                                className="w-full h-48 sm:h-64 md:h-80 lg:h-96 object-cover transform hover:scale-105 transition-transform duration-700"
                            />
                        </div>
                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

                        {/* Floating title with better positioning */}
                        <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 lg:p-8">
                            <motion.div
                                className="max-w-4xl"
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.3 }}
                            >
                                <motion.h1
                                    className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-white mb-2 sm:mb-4 leading-tight tracking-tight"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.5 }}
                                >
                                    {berita.judul}
                                </motion.h1>

                                {/* Quick meta info in header */}
                                <motion.div
                                    className="flex flex-wrap items-center gap-3 sm:gap-4 text-white/90"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.7 }}
                                >
                                    <div className="flex items-center gap-2 bg-black/30 backdrop-blur-sm px-3 py-1.5 rounded-full">
                                        <Calendar className="w-4 h-4" />
                                        <span className="text-sm font-medium">{berita.tanggal}</span>
                                    </div>
                                    <div className="flex items-center gap-2 bg-black/30 backdrop-blur-sm px-3 py-1.5 rounded-full">
                                        <User className="w-4 h-4" />
                                        <span className="text-sm font-medium">{berita.author}</span>
                                    </div>
                                </motion.div>
                            </motion.div>
                        </div>
                    </div>

                    {/* Article Content Container */}
                    <div className="p-4 sm:p-6 lg:p-8 xl:p-12">
                        {/* Enhanced Meta Information */}
                        <motion.div
                            className="flex flex-wrap items-center justify-between gap-4 mb-8 sm:mb-10 pb-6 sm:pb-8 border-b border-gradient-to-r from-transparent via-gray-200 to-transparent dark:via-gray-700"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.4 }}
                        >
                            <div className="flex flex-wrap items-center gap-3 sm:gap-4">
                                <div className="flex items-center gap-2 bg-gray-100 dark:bg-gray-800 px-3 py-2 rounded-xl text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700">
                                    <Calendar className="w-4 h-4 text-green-600" />
                                    <span className="text-sm font-medium">{berita.tanggal}</span>
                                </div>
                                <div className="flex items-center gap-2 bg-gray-100 dark:bg-gray-800 px-3 py-2 rounded-xl text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700">
                                    <User className="w-4 h-4 text-blue-600" />
                                    <span className="text-sm font-medium">Oleh {berita.author}</span>
                                </div>
                                <div className="flex items-center gap-2 bg-gray-100 dark:bg-gray-800 px-3 py-2 rounded-xl text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700">
                                    <Clock className="w-4 h-4 text-purple-600" />
                                    <span className="text-sm font-medium">
                                        {Math.ceil(berita.deskripsi.split(' ').length / 200)} menit baca
                                    </span>
                                </div>
                            </div>

                            {/* Reading progress indicator */}
                            <div className="hidden lg:flex items-center gap-2 text-gray-500 dark:text-gray-400">
                                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <span className="text-xs font-medium">Artikel Terbaru</span>
                            </div>
                        </motion.div>

                        {/* Enhanced Article Content */}
                        <motion.div
                            className="prose prose-lg sm:prose-xl max-w-none dark:prose-invert prose-headings:text-gray-900 dark:prose-headings:text-white prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:leading-relaxed prose-p:text-base sm:prose-p:text-lg"
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.6 }}
                        >
                            <div className="text-gray-800 dark:text-gray-200 leading-relaxed space-y-6">
                                {formatContent(berita.deskripsi)}
                            </div>
                        </motion.div>

                        {/* Enhanced Article Footer */}
                        <motion.div
                            className="mt-12 sm:mt-16 pt-8 sm:pt-10 border-t border-gradient-to-r from-transparent via-gray-200 to-transparent dark:via-gray-700"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.8 }}
                        >
                            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-6">
                                <div className="flex flex-col gap-2">
                                    <div className="text-sm text-gray-500 dark:text-gray-400">
                                        Dipublikasikan pada {berita.tanggal}
                                    </div>
                                    <div className="text-xs text-gray-400 dark:text-gray-500">
                                        Terakhir diperbarui: {new Date().toLocaleDateString('id-ID')}
                                    </div>
                                </div>
                                <div className="flex items-center gap-3 bg-gray-50 dark:bg-gray-800/50 px-4 py-2 rounded-xl border border-gray-200 dark:border-gray-700">
                                    <Share2 className="w-4 h-4 text-green-600" />
                                    <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">
                                        Bagikan artikel ini
                                    </span>
                                </div>
                            </div>
                        </motion.div>
                    </div>
                </motion.article>

                {/* Back to Home Button */}
                <motion.div
                    className="text-center mt-8"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                >
                    <button
                        onClick={handleGoBack}
                        className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2"
                    >
                        Kembali ke Beranda
                    </button>
                </motion.div>
            </main>
        </div>
    );
}