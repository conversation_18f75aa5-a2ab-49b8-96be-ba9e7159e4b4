import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { BookCheck, BookDashedIcon, BookOpen, Trophy, Folder, LayoutGrid, User2Icon, UserCheck, Settings, ImageIcon } from 'lucide-react';
import AppLogo from './app-logo';
import { pbkdf2 } from 'crypto';
import Profile from '@/pages/settings/profile';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Data Guru',
        href: '/data-guru',
        icon: User<PERSON>heck,
    },
    {
        title: 'Berita',
        href: '/news',
        icon: BookOpen
    },
    {
        title: 'Prestasi',
        href: '/trophy',
        icon: Trophy,
    },
    {
        title: 'Data Header',
        href: '/headerData',
        icon: ImageIcon,
    },
    {
        title: 'Manajemen User',
        href: '/manage-user',
        icon: User2Icon,
    },
    {
        title: 'Setting',
        href: '/setting',
        icon: Settings,
    }
];

export function AppSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
