<?php

namespace App\Http\Controllers;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\File;
use App\Models\Header;
use App\Models\Berita;
use App\Models\User;

class SettingController extends Controller
{
    public function index()
    {
        $headers = Header::all();
        return Inertia::render('setting',[
            'headers' => $headers,
        ]);
    }

    public function addHeader(Request $request)
    {
        $request->validate([
            'image' => 'nullable|image|max:2048', // boleh kosong, jika ingin opsional
            'deskripsi' => 'required|string',
            'title' => 'required|string'
        ]);

        $imagePath = null;

        if ($request->hasFile('image')) {
            // Ambil nama asli file
            $filename = time() . '-' . $request->file('image')->getClientOriginalName();

            // Simpan ke public/berita
            $request->file('image')->move(public_path('header'), $filename);

            // Simpan ke database hanya path relatif
            $imagePath = "header/{$filename}";
        } else {
            return back()->with('error', 'Gagal upload gambar.');
        }

        Header::create([
            'title_header' => $request->input('title'),
            'header_images' => $imagePath, // simpan sebagai string (pastikan kolom bukan JSON)
            'header_deskripsi' => $request->input('deskripsi')
        ]);

        return redirect()->back()->with('success', 'Header berhasil ditambahkan!');
    }

    public function header()
    {
        $data = Header::all();
        return Inertia::render('header', [
            'data' => $data
        ]);
    }

    public function manageUser()
    {
        $users = User::all();
        return Inertia::render('manage-user', [
            'users' => $users
        ]);
    }

    public function addUser(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:6',
        ]);

        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        return back()->with('success', 'User berhasil ditambahkan');
    }

    public function deleteHeader($id)
    {
        $header = Header::findOrFail($id);
        File::delete(public_path($header->header_images));
        $header->delete();
        return redirect()->back()->with('success', 'Header berhasil dihapus!');
    }

    public function destroy($id)
    {
        $user = User::findOrFail($id);
        $user->delete();
        return redirect()->back()->with('success', 'User berhasil dihapus!');
    }

}
