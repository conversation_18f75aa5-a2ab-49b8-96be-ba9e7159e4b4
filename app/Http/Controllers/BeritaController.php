<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Carbon\Carbon;


// Models
use App\Models\Berita;
use App\Models\Prestasi;
use App\Models\Header;


class BeritaController extends Controller
{
    public function index()
    {
        // Ambil berita terbaru untuk ditampilkan di landing page (limit 6)
        $berita = Berita::with('user')
            ->latest('tanggal_berita')
            ->limit(6)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'judul' => $item->judul_berita,
                    'tanggal' => Carbon::parse($item->tanggal_berita)->format('d M Y'),
                    'gambar' => $item->gambar ? url($item->gambar) : null, // ⬅️ ini penting
                    'excerpt' => Str::limit($item->deskripsi_berita, 120),
                    'kategori' => 'Berita',
                    'author' => $item->user->name ?? 'Admin'
                ];
            });


        // Ambil prestasi terbaru untuk ditampilkan di landing page (limit 6)
        $prestasi = Prestasi::orderBy('created_at', 'desc')
            ->limit(6)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'nama_siswa' => $item->nama_siswa,
                    'jenis_prestasi' => $item->jenis_prestasi,
                    'tingkat' => $item->tingkat,
                    'tahun' => $item->tahun,
                    'peringkat' => $item->peringkat,
                    'foto' => $item->foto ? url($item->foto) : null, // ⬅️ ini penting
                ];
            });

        // Ambil data header untuk hero section
        $headers = Header::all()->map(function ($item) {
            return [
                'id' => $item->id,
                'title' => $item->title_header,
                'header_images' => $item->header_images, // Sesuaikan dengan field name di frontend
                'deskripsi' => $item->header_deskripsi,
                'created_at' => $item->created_at
            ];
        });

        return Inertia::render('landingpage/landing', [
            'berita' => $berita,
            'prestasi' => $prestasi,
            'headers' => $headers
        ]);
    }

    public function berita()
    {
        $berita = Berita::with('user')->latest()->get();
        return Inertia::render('berita',[
            'berita' => $berita
        ]);
    }

    public function addBerita(Request $request)
    {
        $request->validate([
            'judul' => 'required|string|max:255',
            'deskripsi' => 'required|string',
            'tanggal' => 'required|date',
            'gambar' => 'required|image|max:2048',
        ]);

        if ($request->hasFile('gambar')) {
            // Ambil nama asli file
            $filename = time() . '-' . $request->file('gambar')->getClientOriginalName();

            // Simpan ke public/berita
            $request->file('gambar')->move(public_path('berita'), $filename);

            // Simpan ke database hanya path relatif
            $gambarPath = "berita/{$filename}";
        } else {
            return back()->with('error', 'Gagal upload gambar.');
        }

        Berita::create([
            'judul_berita'     => $request->judul,
            'deskripsi_berita' => $request->deskripsi,
            'tanggal_berita'   => $request->tanggal,
            'gambar'           => $gambarPath,
            'user_id'          => auth()->user()->id,
        ]);

        return redirect()->back()->with('success', 'Berita berhasil ditambahkan!');
    }

    public function destroy($id)
    {
        $berita = Berita::findOrFail($id);

        // Hapus file gambar dari folder
        $gambarPath = public_path($berita->gambar);
        if (file_exists($gambarPath)) {
            unlink($gambarPath);
        }

        $berita->delete();

        return redirect()->back()->with('success', 'Berita berhasil dihapus.');
    }


    // API endpoint untuk polling data berita
    public function getBeritaApi()
    {
        $berita = Berita::with('user')
            ->latest('tanggal_berita')
            ->limit(6)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'judul' => $item->judul_berita,
                    'tanggal' => Carbon::parse($item->tanggal_berita)->format('d M Y'),
                    'gambar' => $item->gambar ? url($item->gambar) : null, // ⬅️ ini penting
                    'excerpt' => Str::limit($item->deskripsi_berita, 120),
                    'kategori' => 'Berita',
                    'author' => $item->user->name ?? 'Admin'
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $berita
        ]);
    }

    public function detail($id)
    {
        $berita = Berita::with('user')->findOrFail($id);

        // Format data berita untuk frontend
        $beritaData = [
            'id' => $berita->id,
            'judul' => $berita->judul_berita,
            'deskripsi' => $berita->deskripsi_berita,
            'tanggal' => Carbon::parse($berita->tanggal_berita)->format('d M Y'),
            'gambar' => $berita->gambar ? url($berita->gambar) : null, // ⬅️ ini penting
            'author' => $berita->user->name ?? 'Admin',
            'created_at' => $berita->created_at,
            'updated_at' => $berita->updated_at
        ];

        return Inertia::render('landingpage/page/page-berita', [
            'berita' => $beritaData
        ]);
    }

}
